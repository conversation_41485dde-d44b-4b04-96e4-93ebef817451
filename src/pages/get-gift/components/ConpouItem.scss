.couponItem {
  width: 100%;
  height: 160px;
  display: flex;
  // background-color: #fbefdf;
  // border-radius: 8px;
  background: url('../../../assets/images/coupon/conpouBg.png') no-repeat;
  background-size: 100% 100%;

  .leftItem {
    width: 25%;
    height: 100%;
    position: relative;

    .count {
      position: absolute;
      top: 0;
      left: 0;
      color: #e40633;
      font-size: 20px;
      text-align: center;
      font-weight: 500;
      font-family:
        PingFangSC,
        PingFang SC;
      width: 64px;
      height: 48px;
      background: url('../../../assets/images/coupon/countTitle.png') no-repeat;
      background-size: 100% 100%;
    }

    .content {
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      text-align: center;

      .moneyCount {
        margin-top: 30px;
        height: 60px;
        line-height: 60px;
        color: #e60f37;
        font-size: 45px;
        font-weight: 700;
        font-family: LiGothicMed;

        .unit {
          display: inline;
          font-size: 28px;
        }
      }

      .xianzhi {
        font-family:
          PingFangSC,
          PingFang SC;
        font-size: 20px;
        color: #e60f37;
      }
    }
  }
  .rightItem {
    width: 75%;
    padding: 10px 30px 10px;
    box-sizing: border-box;
    position: relative;
    display: flex;
    font-size: 20px;

    .leftContentBox {
      width: 100%;
      font-size: 20px;

      .title {
        padding-top: 10px;
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 700;
        font-size: 24px;
        color: #000000;
        line-height: 34px;
      }

      .time {
        color: #696969;
        margin-top: 0;
        margin-bottom: 10px;
        font-size: 20px;
      }

      .useTitle {
        color: #696969;
      }
    }
  }
}
