import Taro, { usePageScroll, useRouter } from '@tarojs/taro'
import { useEffect, useState } from 'react'
import { Image } from '@tarojs/components'
import Slice65Img from '@/assets/images/index/Slice-65.png'
import Slice66Img from '@/assets/images/index/Slice-66.png'
import useObjAtom from '@/hooks/useObjAtom'
import { drawerState } from '@/store'
import { userinfoState } from '@/store/global'
import { currentThreadIdState } from '@/store/chat'

const NavBarTitle = () => {
  const [height, setHeight] = useState(0)
  const [showDropdown, setShowDropdown] = useState(false)
  const drawer = useObjAtom(drawerState)
  const userinfo = useObjAtom(userinfoState)
  const currentThreadId = useObjAtom(currentThreadIdState)

  useEffect(() => {
    if (Taro.getEnv() === 'WEAPP') {
      const rect = Taro.getMenuButtonBoundingClientRect()
      const { top } = rect || {}
      setHeight(top + 42)
    } else {
      setHeight(100)
    }
  }, [])

  const newChat = () => {
    Taro.showLoading({ title: 'loading...', mask: true })
    Taro.request({
      url: `${process.env.TARO_APP_API_AI}/agent/threads`,
      method: 'POST',
      data: { metadata: { userId: userinfo.val?.userId || 1 } }
    }).then((res) => {
      console.log('新建对话成功:', res)
      Taro.hideLoading()
      const threadId = res.data.thread_id
      currentThreadId.set(threadId)
      Taro.setStorageSync('threadId', threadId)
      Taro.navigateTo({
        url: '/pages/chat/index',
        success: () => {
          drawer.set(false)
        }
      })
    })
  }

  return (
    <>
      <div className="bg-white flex items-end w-full fixed top-0 z-50" style={{ height: `${height}px` }}>
        <div className="flex items-center h-[108px] w-full">
          <div
            onClick={() => {
              userinfo.val?.phone && drawer.set(true)
            }}
            className="w-[84px] h-full flex_center"
          >
            {userinfo.val?.phone && <Image className="anim_btn w-[36px] h-[36px]" src={Slice65Img} />}
          </div>
          <div className="flex-1 flex_center cursor-pointer relative" onClick={() => setShowDropdown(!showDropdown)}>
            <div className="text-[32px] text-[#2A3447] mr-[4px]">爱定制</div>
            <Image className="anim_btn w-[30px] h-[30px] flex_center" src={Slice66Img} />
          </div>
          <div className="w-[84px]"></div>
        </div>
      </div>
      <div className="bg-white flex items-end w-full" style={{ height: `${height}px` }}></div>

      {/* 遮罩层 */}
      {showDropdown && <div className="fixed inset-0 z-[9998]" onClick={() => setShowDropdown(false)} />}

      {/* 下拉菜单 */}
      {showDropdown && (
        <div
          className="fixed z-[9999]"
          style={{
            top: `${height - 5}px`,
            left: '50%',
            transform: 'translateX(-50%)'
          }}
        >
          {/* 三角标 */}
          <div
            className="w-0 h-0 mx-auto"
            style={{
              borderLeft: '8px solid transparent',
              borderRight: '8px solid transparent',
              borderBottom: '8px solid white',
              filter: 'drop-shadow(0 -2px 4px rgba(0, 0, 0, 0.1))'
            }}
          />

          {/* 菜单内容 */}
          <div className="bg-white rounded-[16px] shadow-lg px-[20px] py-[16px]">
            <div
              className="flex items-center justify-center h-[50px] text-[26px] text-[#333] cursor-pointer hover:bg-gray-50 rounded-[8px] transition-colors px-[24px] whitespace-nowrap"
              onClick={() => {
                setShowDropdown(false)
                newChat()
              }}
            >
              开始新对话
            </div>
          </div>
        </div>
      )}
    </>
  )
}

export default NavBarTitle
