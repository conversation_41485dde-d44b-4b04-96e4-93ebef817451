import useObj<PERSON>tom from '@/hooks/useObjAtom'
import { userinfoState } from '@/store/global'
import { WebView } from '@tarojs/components'
import Taro, { useRouter } from '@tarojs/taro'

const TShirtCanvas = () => {
  const router = useRouter()
  const { id = '', threadId = '', messageId = '', from = '', imgUrl = '' } = router.params
  let openId = Taro.getStorageSync('__weapp_open_id__') as string
  // const newToken = token.split(' ')
  // const tokenEnd = newToken[newToken.length - 1]
  // const userinfo = useObjAtom(userinfoState)

  // id // 模板ID
  // userId // 用户ID
  // token
  // threadId // 对话ID
  // messageId // 消息ID
  // from // 从哪里跳转的
  // imgUrl // 图案
  // openId
  return (
    <>
      <WebView
        src={`${process.env.TARO_APP_H5}/canvas?id=${id}&threadId=${threadId}&messageId=${messageId}&imgUrl=${imgUrl}&from=${from}&openId=${openId}`}
      />
    </>
  )
}

export default TShirtCanvas
