import { api1Request } from '@/utils/request'

export interface ComputePriceData {
  totalNum: number
  totalPrice: number
  frontPrintingId: number
  frontPrinting: string
  frontArea: number
  frontPrice: number
  backPrintingId: number
  backPrinting: string
  backArea: number
  backPrice: number
}

// 响应接口
export interface ReadyTemplateSaveRes {
  data: {
    id: number
    title: string
    des: string
    imageUrl: string
    printingImage: string
    price: number
    delPrice: number
    diyData: string
    templateCode: string
    computePriceData: ComputePriceData
    styleCode: string
    colour: string
    gender: string
    printingProcessId: number
    tagList: {
      id: number
      tagName: string
    }[]
    customNum: number
    designNum: number
    designUser: string
    designUserHead: string
    collect: number
    content: string
  }
}

/**
 * 成衣模版详情
 * @param {string} id 成衣模版ID
 * @returns
 */
export function readyTemplateSave(id: string): Promise<ReadyTemplateSaveRes> {
  return api1Request.get({
    url: `/api/index/anon/ready/template/detail?id=${id}`
  })
}

// MALE(1, "男"),
// FEMALE(2, "女"),
// CHILD(3, "儿童"),
// ADULT(4, "成人");
export const GenderMap = {
  BOY: '男',
  GIRL: '女',
  CHILD: '儿童',
  ADULT: '成人'
}

// WHITE(1, "白"),
// BLACK(2, "黑"),
// ICE_BLUE(3, "冰蓝"),
// DAI_PINK(4, "黛粉"),
// CLASSIC_WHITE(5, "经典白"),
// CHARM_GRAY(6, "魅力灰"),
// SOFT_LIGHT_WHITE(7, "柔光白"),
// LAZY_BLACK(8, "慵懒黑"),
// MINT_GREEN(9, "薄荷绿");
export const ColourMap = {
  WHITE: { title: '白色', color: '#ffffff' },
  BLACK: { title: '黑色', color: '#000000' },
  ICE_BLUE: { title: '冰蓝', color: '#ADD8E6' },
  DAI_PINK: { title: '黛粉', color: '#FFC0CB' },
  CLASSIC_WHITE: { title: '经典白', color: '#F5F5F5' },
  CHARM_GRAY: { title: '魅力灰', color: '#D3D3D3' },
  SOFT_LIGHT_WHITE: { title: '柔光白', color: '#FAFAFA' },
  LAZY_BLACK: { title: '慵懒黑', color: '#2F2F2F' },
  MINT_GREEN: { title: '薄荷绿', color: '#98FF98' }
}

// 响应接口
export interface CanvasRes {
  data: {
    genders: string[]
    colours: string[]
    viewAngle: {
      imageUrl: string
      maskImageUrl: string
      key: string
    }[]
    patternStyles: {
      id: number
      name: string
      styleCode: string
      price: number
      imageUrl: string
    }[]
    printingProcessList: {
      id: number
      name: string
      price: number
      imageUrl: string
      subList: {}[]
    }[]
  }
}

/**
 * 画布详情
 * @param {string} templateCode 母版CODE
 * @returns
 */
export function getCanvas(templateCode: string): Promise<CanvasRes> {
  return api1Request.get({
    url: `/api/canvas/detail?templateCode=${templateCode}`
  })
}

// 参数接口
export interface ComputePriceParams {
  /*模板CODE */
  masterTemplateCode?: string
  /*款式CODE */
  styleTemplateCode?: string
  /*款式 */
  gender?: string
  /*前面DIY数据 */
  frontDiyData?: string
  /*后面DIY数据 */
  backDiyData?: string
  /*前工艺ID */
  frontPrintingId?: number
  /*后工艺ID */
  backPrintingId?: number
  /*购买数量 */
  buyNum?: number
  /*尺码CODE */
  sizeCode: string
}

// 响应接口
export interface ComputePriceRes {
  code: number
  msg: string
  data: {
    totalNum: number
    totalPrice: number
    frontPrintingId: number
    frontPrinting: string
    frontArea: number
    frontPrice: number
    backPrintingId: number
    backPrinting: string
    backArea: number
    backPrice: number
    basePrice: number
  }
}

/**
 * 计算价格
 * @param {object} params 用户注册
 * @param {string} params.masterTemplateCode 模板CODE
 * @param {string} params.styleTemplateCode 款式CODE
 * @param {string} params.gender 款式
 * @param {string} params.frontDiyData 前面DIY数据
 * @param {string} params.backDiyData 后面DIY数据
 * @param {number} params.frontPrintingId 前工艺ID
 * @param {number} params.backPrintingId 后工艺ID
 * @param {number} params.buyNum 购买数量
 * @param {string} params.sizeCode 尺码CODE
 * @returns
 */
export function computePrice(params: ComputePriceParams): Promise<ComputePriceRes> {
  return api1Request.post({
    url: `/api/canvas/compute/price`,
    data: params
  })
}

// 参数接口
export interface ReadyTemplateSaveParams {
  /*标题 */
  title?: string
  /*封面图片地址 */
  imageUrl?: string
  /*价格 */
  price?: number
  /*diy的json数据 */
  diyData?: string
  /*母版CODE */
  templateCode?: string
  /*版型样式code */
  styleCode?: string
  /*颜色 */
  colour?: string
  /*男、女、儿童 */
  gender?: string
  /*标签ID列表 */
  tagIds?: number[]
  /*印花图片列表多个逗号分隔 */
  printingImage?: string
  /*计算价格数据 */
  computePriceData?: {
    /*总购买数量 */
    totalNum?: number
    /*总价格 */
    totalPrice?: number
    /*前面工艺ID */
    frontPrintingId?: number
    /*前面工艺 */
    frontPrinting?: string
    /*前面面积 (单位:平方厘米㎡) */
    frontArea?: number
    /*前面价格 */
    frontPrice?: number
    /*后面工艺ID */
    backPrintingId?: number
    /*后面工艺 */
    backPrinting?: string
    /*后面面积 (单位:平方厘米㎡) */
    backArea?: number
    /*后面价格 */
    backPrice?: number
  }
  channel: string
}

// 响应接口
export interface TemplateSaveRes {
  data: number
}

/**
 * 成衣模版保存
 * @param {object} params 成衣模版数据传输对象
 * @param {string} params.title 标题
 * @param {string} params.imageUrl 封面图片地址
 * @param {number} params.price 价格
 * @param {string} params.diyData diy的json数据
 * @param {string} params.templateCode 母版CODE
 * @param {string} params.styleCode 版型样式code
 * @param {string} params.colour 颜色
 * @param {string} params.gender 男、女、儿童
 * @param {array} params.tagIds 标签ID列表
 * @param {string} params.printingImage 印花图片列表多个逗号分隔
 * @param {object} params.computePriceData 计算价格数据
 * @returns
 */
export function templateSave(params: ReadyTemplateSaveParams): Promise<TemplateSaveRes> {
  return api1Request.post({
    url: `/api/index/ready/template/save`,
    data: params
  })
}
